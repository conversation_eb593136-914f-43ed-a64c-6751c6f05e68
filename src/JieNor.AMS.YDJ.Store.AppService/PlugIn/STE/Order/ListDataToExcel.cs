using System;
using System.Linq;
using System.Collections.Generic;
using System.Data;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.PlugIn.STE.Order
{
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("listdatatoexcel")]
    public class ListDataToExcel : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 自定义事件中，提供额外过滤条件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName)
            {
                case "afterListData":
                    var data = e.EventData as QueryDataInfo;
                    UpDataInfo(data);
                    break;
            }
        }

        /// <summary>
        /// 导出的时候更新
        /// </summary>
        /// <param name="datas"></param>
        private void UpDataInfo(QueryDataInfo datas)
        {
            HtmlForm lookupForm = this.OperationContext?.HtmlForm;
            if (datas == null || (datas != null && datas.OfficeDatas == null)) return;
            if (!datas.OfficeDatas.ContainsKey("fstoreid")) return;

            // 获取数据状态和销售部门数据
            var fstatusData = datas.OfficeDatas.ContainsKey("fstatus") ? datas.OfficeDatas["fstatus"] : null;
            var fdeptidData = datas.OfficeDatas.ContainsKey("fdeptid") ? datas.OfficeDatas["fdeptid"] : null;
            var fstoreidData = datas.OfficeDatas["fstoreid"];

            if (fstatusData == null || fdeptidData == null || fstoreidData == null) return;

            // 遍历每一行数据
            for (int i = 0; i < fstatusData.Count; i++)
            {
                var status = Convert.ToString(fstatusData[i]);
                var deptId = Convert.ToString(fdeptidData[i]);

                // 当数据状态不为已提交(D)或已审核(E)时，需要处理fstoreid
                if (!status.Equals("D") && !status.Equals("E") && !deptId.IsNullOrEmptyOrWhiteSpace())
                {
                    var storeCode = GetStoreCodeByDeptId(deptId);
                    if (!storeCode.IsNullOrEmptyOrWhiteSpace())
                    {
                        fstoreidData[i] = storeCode;
                    }
                }
            }
        }

        /// <summary>
        /// 根据销售部门ID获取关联门店编码
        /// </summary>
        /// <param name="deptId">销售部门ID</param>
        /// <returns>门店编码</returns>
        private string GetStoreCodeByDeptId(string deptId)
        {
            var sql = @"SELECT store.fnumber
                           FROM t_bd_department dept
                           INNER JOIN t_bas_store store ON store.fid = dept.fstore
                           WHERE dept.fid = @deptId AND dept.fmainorgid = @fmainorgid";

            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@deptId", DbType.String, deptId),
                new SqlParam("@fmainorgid", DbType.String, this.Context.Company)
            };

            var result = this.DBService.ExecuteDynamicObject(this.Context, sql, sqlParams);

            if (result != null && result.Any())
            {
                return Convert.ToString(result[0]["fnumber"]);
            }

            return string.Empty;
        }
    }
}